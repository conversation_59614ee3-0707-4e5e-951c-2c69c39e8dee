
import { useMemo } from "react"
import { useStatsGraphQL } from "@/hooks/use-graphql-influencers"
import { useTranslations } from "@/hooks/use-translations"
import { UnifiedFilterToolbar } from "./unified-filter-toolbar"

// Tipos para estatísticas otimizadas
interface BaseStats {
  totalInfluencers: number;
  totalViews: number;
  totalFollowers: number;
  totalBrands: number;
}

interface InfluencerData {
  id: string;
  instagramFollowers?: number;
  tiktokFollowers?: number;
  youtubeFollowers?: number;
  instagramViews?: number;
  tiktokViews?: number;
  youtubeViews?: number;
}

interface AnimatedStatsProps {
  // Estatísticas base (calculadas no servidor) - opcional para compatibilidade
  baseStats?: BaseStats;
  // Dados dos influencers filtrados (para cálculo local)
  filteredInfluencers?: InfluencerData[];
  // Contagem atual (para compatibilidade)
  influencersCount: number;
  userId?: string;
  // Props para FilterBar
  searchTerm?: string;
  selectedCategory?: string;
  onSearchChange?: (value: string) => void;
  onCategoryChange?: (category: string) => void;
  // Props para Toolbar
  viewMode?: "grid" | "list";
  selectionMode?: boolean;
  selectedCount?: number;
  onViewModeChange?: (mode: "grid" | "list") => void;
  onAddInfluencer?: () => void;
  onToggleSelectionMode?: () => void;
  onDeleteSelected?: () => void;
  onDuplicateSelected?: () => void;
  onBrandFilterChange?: (selectedBrands: string[]) => void;
  verifiedOnly?: boolean;
  onVerifiedOnlyChange?: (value: boolean) => void;
  onSendToBrand?: (brandId: string, brandName: string) => void;
}

export function AnimatedStats({
  baseStats,
  filteredInfluencers,
  influencersCount,
  userId,
  searchTerm,
  selectedCategory,
  onSearchChange,
  onCategoryChange,
  // Props da Toolbar
  viewMode,
  selectionMode,
  selectedCount,
  onViewModeChange,
  onAddInfluencer,
  onToggleSelectionMode,
  onDeleteSelected,
  onDuplicateSelected,
  onBrandFilterChange,
  verifiedOnly,
  onVerifiedOnlyChange,
  onSendToBrand
}: AnimatedStatsProps) {
  const { t } = useTranslations();

  // 🔄 COMPATIBILIDADE: Fallback para hook GraphQL quando baseStats não fornecido
  const effectiveUserId = userId || 'system';
  const { stats: fallbackStats } = useStatsGraphQL(
    baseStats ? '' : effectiveUserId // Só executa se baseStats não existir
  );

  // Usar baseStats se fornecido, senão usar fallback do GraphQL
  const effectiveBaseStats = baseStats || fallbackStats || {
    totalInfluencers: 0,
    totalViews: 0,
    totalFollowers: 0,
    totalBrands: 0
  };

  // Detectar se há filtros ativos
  const hasActiveFilters = useMemo(() => {
    const hasSearch = searchTerm && searchTerm.trim().length > 0;
    const hasCategory = selectedCategory && selectedCategory !== 'all' && selectedCategory !== '';
    const hasVerified = verifiedOnly === true;

    return hasSearch || hasCategory || hasVerified;
  }, [searchTerm, selectedCategory, verifiedOnly]);

  // � OTIMIZAÇÃO: Calcular estatísticas localmente dos dados filtrados
  const filteredStats = useMemo(() => {
    if (!filteredInfluencers || filteredInfluencers.length === 0) {
      return { totalViews: 0, totalFollowers: 0 };
    }

    let totalFollowers = 0;
    let totalViews = 0;

    filteredInfluencers.forEach(influencer => {
      // Somar seguidores de todas as plataformas
      totalFollowers += (influencer.instagramFollowers || 0) +
                       (influencer.tiktokFollowers || 0) +
                       (influencer.youtubeFollowers || 0);

      // Somar views de todas as plataformas
      totalViews += (influencer.instagramViews || 0) +
                   (influencer.tiktokViews || 0) +
                   (influencer.youtubeViews || 0);
    });

    return { totalViews, totalFollowers };
  }, [filteredInfluencers]);

  // 🚀 OTIMIZAÇÃO: Lógica otimizada para escolher estatísticas com validação
  const displayStats = useMemo(() => {
    if (hasActiveFilters && filteredInfluencers) {
      // Usar estatísticas calculadas localmente dos dados filtrados
      return {
        totalInfluencers: influencersCount || 0,
        totalViews: filteredStats.totalViews,
        totalFollowers: filteredStats.totalFollowers,
        isFiltered: true,
        sourceType: 'filtered-local'
      };
    }

    // Usar estatísticas base do servidor (com fallback para valores padrão)
    return {
      totalInfluencers: effectiveBaseStats.totalInfluencers || 0,
      totalViews: effectiveBaseStats.totalViews || 0,
      totalFollowers: effectiveBaseStats.totalFollowers || 0,
      isFiltered: false,
      sourceType: baseStats ? 'server-cached' : 'graphql-fallback'
    };
  }, [hasActiveFilters, filteredInfluencers, influencersCount, filteredStats, effectiveBaseStats, baseStats]);

  // Formatador para exibir números com separação de milhares
  const formatNumber = (num: number): string => {
    if (typeof num !== 'number' || isNaN(num)) {
      return '0';
    }
    return num.toLocaleString('pt-BR');
  };

  return (
    <div className="sticky top-0 z-5 w-full bg-background backdrop-blur-sm border-b border-border/40 ">
      <div className="px-2 py-4 space-y-4">
        
        {/* Container para os insights estáticos */}
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="hidden md:flex items-center gap-6 text-foreground dark:text-white mr-auto">
            
            {/* Estatística de Influencers */}
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="url(#gradient-influencers)" className="w-5 h-5 mr-2">
                <defs>
                  <linearGradient id="gradient-influencers" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#9810fa" />
                    <stop offset="100%" stopColor="#ff0074" />
                  </linearGradient>
                </defs>
                <path d="M12 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8zm0-2a2 2 0 1 1 0-4 2 2 0 0 1 0 4z" />
                <path d="M12 12c-5.5 0-10 3.5-10 8v1h20v-1c0-4.5-4.5-8-10-8zm0 2c4.5 0 8 2.5 8 6H4c0-3.5 3.5-6 8-6z" />
              </svg>
              <span className="font-semibold text-sm">{formatNumber(displayStats.totalInfluencers)}</span>
              <span className="text-muted-foreground dark:text-white ml-1 text-sm">
                {displayStats.isFiltered ?
                  String(t('common.found')).toUpperCase() :
                  String(t('navigation.influencers')).toUpperCase()
                }
              </span>
              {displayStats.isFiltered && (
                <span className="text-xs text-muted-foreground ml-1">(filtrados)</span>
              )}
            </div>
            
            <div className="text-muted-foreground dark:text-white">|</div>
            
            {/* Estatística de Views */}
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="url(#gradient-views)" className="w-5 h-5 mr-2">
                <defs>
                  <linearGradient id="gradient-views" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#9810fa" />
                    <stop offset="100%" stopColor="#ff0074" />
                  </linearGradient>
                </defs>
                <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z" />
              </svg>
              <span className="font-semibold text-sm">{formatNumber(displayStats.totalViews)}</span>
              <span className="text-muted-foreground dark:text-white ml-1 text-sm">{String(t('panels.contact.total_views')).toUpperCase()}</span>
            </div>
            
            <div className="text-muted-foreground dark:text-white">|</div>
            
            {/* Estatística de Seguidores */}
            <div className="flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="url(#gradient-followers)" className="w-5 h-5 mr-2">
                <defs>
                  <linearGradient id="gradient-followers" x1="0%" y1="0%" x2="100%" y2="0%">
                    <stop offset="0%" stopColor="#9810fa" />
                    <stop offset="100%" stopColor="#ff0074" />
                  </linearGradient>
                </defs>
                <path d="M16 11c1.66 0 2.99-1.34 2.99-3S17.66 5 16 5s-3 1.34-3 3 1.34 3 3 3zm-8 0c1.66 0 2.99-1.34 2.99-3S9.66 5 8 5 5 6.34 5 8s1.34 3 3 3zm0 2c-2.33 0-7 1.17-7 3.5V19h14v-2.5c0-2.33-4.67-3.5-7-3.5zm8 0c-.29 0-.62.02-.97.05 1.16.84 1.97 1.97 1.97 3.45V19h6v-2.5c0-2.33-4.67-3.5-7-3.5z" />
              </svg>
              <span className="font-semibold text-sm">{formatNumber(displayStats.totalFollowers)}</span>
              <span className="text-muted-foreground dark:text-white ml-1 text-sm">{String(t('panels.contact.total_followers')).toUpperCase()}</span>
            </div>

            {/* Indicador de fonte dos dados */}
            {displayStats.sourceType === 'filtered-local' && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-xs">Dados filtrados</span>
              </div>
            )}

            {displayStats.sourceType === 'server-cached' && (
              <div className="flex items-center gap-2 text-muted-foreground">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-xs">Dados totais</span>
              </div>
            )}

            
            
          </div>
        </div>
        
        {/* Filtros integrados */}
        {/* Barra unificada de filtros e toolbar */}
        {(onSearchChange && onCategoryChange) && (onViewModeChange && onAddInfluencer) && (
          <UnifiedFilterToolbar
            // FilterBar props
            searchTerm={searchTerm || ''}
            selectedCategory={selectedCategory || 'all'}
            onSearchChange={onSearchChange}
            onCategoryChange={onCategoryChange}
            onBrandFilterChange={onBrandFilterChange}
            userId={userId} // 🔧 CORREÇÃO: Passar userId
            // Toolbar props
            viewMode={viewMode || "grid"}
            selectionMode={selectionMode || false}
            selectedCount={selectedCount || 0}
            onViewModeChange={onViewModeChange}
            onAddInfluencer={onAddInfluencer}
            onToggleSelectionMode={onToggleSelectionMode || (() => {})}
            onDeleteSelected={onDeleteSelected || (() => {})}
            onDuplicateSelected={onDuplicateSelected || (() => {})}
            verifiedOnly={verifiedOnly || false}
            onVerifiedOnlyChange={onVerifiedOnlyChange || (() => {})}
            onSendToBrand={onSendToBrand}
          />
        )}
        
      </div>
    </div>
  );
}


