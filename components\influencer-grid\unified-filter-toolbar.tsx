

import React, { useState } from "react"
import { 
  Search, 
  Filter, 
  X, 
  Building2, 
  Check,
  GridIcon, 
  List, 
  Plus, 
  Trash2, 
  <PERSON><PERSON>, 
  MessageSquare, 
  CheckCircle, 
  Send, 
  Settings
} from "lucide-react"
import Link from "next/link"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { 
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card } from "@/components/ui/card"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Input } from "@/components/ui/input"
import { 
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Protect } from "@clerk/nextjs"
import { useCategories } from "@/hooks/use-graphql-influencers"
import { useBrandsList } from "@/hooks/use-brands"
import { useTranslations } from "@/hooks/use-translations"
import { Brand } from "@/types/brand"

interface UnifiedFilterToolbarProps {
  // FilterBar props
  searchTerm: string
  selectedCategory: string
  onSearchChange: (value: string) => void
  onCategoryChange: (category: string) => void
  onBrandFilterChange?: (selectedBrands: string[]) => void
  userId?: string // 🔧 CORREÇÃO: Adicionar userId como prop

  // Toolbar props
  viewMode: "grid" | "list"
  selectionMode: boolean
  selectedCount: number
  onViewModeChange: (mode: "grid" | "list") => void
  onAddInfluencer: () => void
  onToggleSelectionMode: () => void
  onDeleteSelected: () => void
  onDuplicateSelected: () => void
  verifiedOnly: boolean
  onVerifiedOnlyChange: (value: boolean) => void
  onSendToBrand?: (brandId: string, brandName: string) => void
}

// Componente da barra de pesquisa compacta
function CompactSearchInput({ searchTerm, onSearchChange, placeholder }: {
  searchTerm: string
  onSearchChange: (value: string) => void
  placeholder: string
}) {
  return (
    <div className="relative group">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground group-focus-within:text-[#ff0074] transition-colors duration-200" />
      <input 
        type="text" 
        className="w-full h-8 pl-10 pr-8 border border-border bg-background text-foreground rounded-lg text-sm
                   focus:outline-none focus:ring-2 focus:ring-[#ff0074]/20 focus:border-[#ff0074] 
                   placeholder:text-muted-foreground transition-all duration-200
                   hover:border-[#ff0074]/50 shadow-sm hover:shadow-md" 
        placeholder={placeholder}
        value={searchTerm}
        onChange={(e) => onSearchChange(e.target.value)}
      />
      {searchTerm && (
        <Button
          className="absolute right-2 top-1/2 transform -translate-y-1/2 h-4 w-4 p-0 hover:bg-muted rounded-full"
          onClick={() => onSearchChange('')}
        >
          <X className="w-3 h-3" />
        </Button>
      )}
    </div>
  )
}

// Componente do seletor de categoria compacto
function CompactCategorySelector({ selectedCategory, onCategoryChange, categories, error }: {
  selectedCategory: string
  onCategoryChange: (category: string) => void
  categories: Array<{ id: string; name: string; slug: string }>
  error: any
}) {
  const selectedCategoryData = categories.find((cat) => cat.id === selectedCategory)

  // 🔍 DEBUG: Log das categorias no dropdown
  console.log('🏷️ [DROPDOWN CATEGORIES]', {
    selectedCategory,
    categoriesCount: categories.length,
    categories: categories.map(cat => ({ id: cat.id, name: cat.name })),
    selectedCategoryData
  });
  
  return (
    <div className="flex items-center gap-2 text-sm">
      
      
      {error ? (
        <Badge className="bg-destructive/10 text-destructive border-destructive/20 text-xs px-2 py-1">
          Erro
        </Badge>
      ) : (
        <Select value={selectedCategory} onValueChange={onCategoryChange}>
          <SelectTrigger className="w-36  text-sm h-8 border-border bg-background 
                                   hover:border-[#ff0074]/50 focus:ring-2 focus:ring-[#ff0074]/20 
                                   focus:border-[#ff0074] transition-all duration-200 rounded-lg">
            <div className="flex items-center text-sm gap-2 w-full">
              {selectedCategory !== 'all' && selectedCategoryData && (
                <div className="w-2 h-2 bg-[#ff0074] rounded-full flex-shrink-0" />
              )}
              <SelectValue
                placeholder="Categoria"
                className="text-left truncate text-sm"
              >
                {selectedCategoryData?.name || "Todas as categorias"}
              </SelectValue>
            </div>
          </SelectTrigger>
          
          <SelectContent className="bg-popover border-border shadow-xl rounded-xl max-h-64 overflow-hidden">
            <div className="p-1">
              {categories.map((category) => (
                <SelectItem 
                  key={category.id} 
                  value={category.id}
                  className="text-sm cursor-pointer text-sm focus:bg-[#ff0074]/10 focus:text-[#ff0074] 
                           hover:bg-accent hover:text-accent-foreground transition-colors duration-150
                           rounded-lg my-0.5 px-2 py-1.5"
                >
                  <div className="flex items-center justify-between w-full gap-2">
                    <div className="flex items-center gap-2">
                      {category.id !== 'all' && (
                        <div className="w-2 h-2 bg-[#5600ce] rounded-full flex-shrink-0" />
                      )}
                      <span className="truncate font-medium">{category.name}</span>
                    </div>
                    
                    {selectedCategory === category.id && (
                      <Badge className="bg-[#ff0074] text-white text-xs px-1.5 py-0.5 rounded-full">
                        ✓
                      </Badge>
                    )}
                  </div>
                </SelectItem>
              ))}
            </div>
          </SelectContent>
        </Select>
      )}
    </div>
  )
}

// Componente do seletor de marcas compacto
function CompactBrandSelector({ onBrandFilterChange }: {
  onBrandFilterChange?: (selectedBrands: string[]) => void
}) {
  const { brands, loading, error } = useBrandsList()
  const [selectedBrands, setSelectedBrands] = useState<string[]>([])
  const [searchTerm, setSearchTerm] = useState("")

  const toggleBrand = (brandId: string) => {
    const newSelection = selectedBrands.includes(brandId)
      ? selectedBrands.filter(id => id !== brandId)
      : [...selectedBrands, brandId]
    
    setSelectedBrands(newSelection)
    onBrandFilterChange?.(newSelection)
  }

  const clearBrands = () => {
    setSelectedBrands([])
    onBrandFilterChange?.([])
  }

  const filteredBrands = brands.filter(brand => 
    brand.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  const getDisplayText = () => {
    if (selectedBrands.length === 0) return "Marcas"
    if (selectedBrands.length === 1) {
      const brand = brands.find(b => b.id === selectedBrands[0])
      return brand?.name || "Marca"
    }
    return `${selectedBrands.length} marcas`
  }

  return (
    <Protect role="org:admin">
      <div className="flex items-center gap-2">
        
        
        {error ? (
          <Badge className="bg-destructive/10 text-destructive border-destructive/20 text-xs px-2 py-1">
            Erro
          </Badge>
        ) : (
          <Popover>
            <PopoverTrigger asChild>
              <button
                className="w-36 h-8 text-sm border border-border bg-background 
                          hover:border-[#5600ce]/50 focus:ring-2 focus:ring-[#5600ce]/20 
                          focus:border-[#5600ce] transition-all duration-200 rounded-lg
                          flex items-center justify-between px-3 py-2"
              >
                <div className="flex items-center text-sm gap-2 w-full">
                  {selectedBrands.length > 0 && (
                    <div className="w-2 h-2 bg-[#5600ce] rounded-full flex-shrink-0" />
                  )}
                  <span className="text-left truncate text-sm">
                    {getDisplayText()}
                  </span>
                </div>
                <svg
                  className="h-4 w-4 opacity-50"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 9l-7 7-7-7"
                  />
                </svg>
              </button>
            </PopoverTrigger>
            
            <PopoverContent className="w-80 p-0" align="start">
              <div className="p-3 bg-popover border-border shadow-xl rounded-xl">
                {/* Busca */}
                <div className="mb-3">
                  <Input
                    placeholder="Buscar marcas..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="h-8 text-sm"
                  />
                </div>
                
                {/* Opção limpar */}
                {selectedBrands.length > 0 && (
                  <button
                    onClick={clearBrands}
                    className="w-full text-left p-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground 
                             transition-colors duration-150 rounded-lg mb-2 border-b border-border flex items-center gap-2"
                  >
                    <X className="w-3 h-3 text-muted-foreground" />
                    <span className="font-medium">Limpar seleção</span>
                  </button>
                )}
                
                {loading ? (
                  <div className="text-sm text-muted-foreground py-4 text-center">
                    Carregando marcas...
                  </div>
                ) : filteredBrands.length === 0 ? (
                  <div className="text-sm text-muted-foreground py-4 text-center">
                    {searchTerm ? "Nenhuma marca encontrada" : "Nenhuma marca"}
                  </div>
                ) : (
                  <ScrollArea className="h-48">
                    <div className="space-y-1">
                      {filteredBrands.map((brand) => (
                        <button
                          key={brand.id}
                          onClick={() => toggleBrand(brand.id)}
                          className="w-full text-left p-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground 
                                   transition-colors duration-150 rounded-lg"
                        >
                          <div className="flex items-center justify-between w-full gap-2">
                            <div className="flex items-center gap-2">
                              <div className="flex items-center justify-center w-4 h-4">
                                {selectedBrands.includes(brand.id) && (
                                  <Check className="w-3 h-3 text-[#5600ce]" />
                                )}
                              </div>
                              {brand.logo && (
                                <img 
                                  src={brand.logo} 
                                  alt={brand.name}
                                  className="w-4 h-4 rounded object-cover"
                                />
                              )}
                              <span className="truncate font-medium">{brand.name}</span>
                            </div>
                            
                            {selectedBrands.includes(brand.id) && (
                              <div className="bg-[#5600ce] text-white text-xs px-1.5 py-0.5 rounded-full">
                                ✓
                              </div>
                            )}
                          </div>
                        </button>
                      ))}
                    </div>
                  </ScrollArea>
                )}
              </div>
            </PopoverContent>
          </Popover>
        )}
      </div>
    </Protect>
  )
}

export function UnifiedFilterToolbar({
  // FilterBar props
  searchTerm,
  selectedCategory,
  onSearchChange,
  onCategoryChange,
  onBrandFilterChange,
  userId,

  // Toolbar props
  viewMode,
  selectionMode,
  selectedCount,
  onViewModeChange,
  onAddInfluencer,
  onToggleSelectionMode,
  onDeleteSelected,
  onDuplicateSelected,
  verifiedOnly,
  onVerifiedOnlyChange,
  onSendToBrand
}: UnifiedFilterToolbarProps) {
  const { t } = useTranslations()
  const { categories, error } = useCategories(userId) // 🔧 CORREÇÃO: Passar userId
  const { brands, loading: isLoadingBrands } = useBrandsList()
  const [isPopoverOpen, setIsPopoverOpen] = useState(false)

  // 🔍 DEBUG: Log para debugar categorias
  console.log('🔍 [CATEGORIES DEBUG]', {
    categories: categories,
    selectedCategory: selectedCategory,
    error: error
  });

  const handleSendToBrand = (brand: Brand) => {
    if (onSendToBrand) {
      onSendToBrand(brand.id, brand.name)
      setIsPopoverOpen(false)
    }
  }

  return (
    <div className="px-4 py-3 bg-background/50 backdrop-blur-sm rounded-lg">
      <div className="flex items-center justify-between gap-4">
        {/* Lado esquerdo - Filtros */}
        <div className="flex items-center gap-3 flex-1 min-w-0">
          {/* Busca */}
          <div className="w-full max-w-xs">
            <CompactSearchInput
              searchTerm={searchTerm}
              onSearchChange={onSearchChange}
              placeholder="Pesquisar..."
            />
          </div>
          
          
          
          {/* Seletores */}
          <div className="flex items-center gap-3">
            <CompactCategorySelector
              selectedCategory={selectedCategory}
              onCategoryChange={onCategoryChange}
              categories={categories}
              error={error}
            />
            
            <CompactBrandSelector 
              onBrandFilterChange={onBrandFilterChange}
            />
          </div>
          
       
          
          {/* Filtro de verificados */}
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                size="sm"
                className={`h-8 px-3 gap-2 ${
                  verifiedOnly 
                    ? 'bg-[#ff0074] hover:bg-[#cc0050] text-white' 
                    : 'bg-muted/50 hover:bg-muted text-muted-foreground hover:text-foreground'
                }`}
                onClick={() => onVerifiedOnlyChange(!verifiedOnly)}
              >
                <CheckCircle className="h-3 w-3" />
                <span className="text-xs font-medium hidden lg:inline">Exclusivos</span>
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>Mostrar apenas influenciadores verificados</p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Lado direito - Controles */}
        <div className="flex items-center gap-3">
          {selectionMode && selectedCount > 0 ? (
            // Ações de seleção
            <>
              <Badge className="bg-[#ff0074]/10 text-[#ff0074] border-[#ff0074]/20 px-2 py-1 text-xs font-medium">
                {selectedCount} selecionado{selectedCount > 1 ? 's' : ''}
              </Badge>

              {onSendToBrand && (
                <Popover open={isPopoverOpen} onOpenChange={setIsPopoverOpen}>
                  <PopoverTrigger asChild>
                    <Button 
                      size="sm"
                      className="h-8 px-3 gap-2 bg-[#9810fa] hover:bg-[#4a00b8] text-white"
                    >
                      <Send className="h-3 w-3" />
                      <span className="text-xs hidden sm:inline">Enviar</span>
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-64 p-0" align="end">
                    <Card className="border-0 shadow-lg">
                      <div className="p-3">
                        <h4 className="font-semibold text-sm mb-2">Selecionar Marca</h4>
                        {isLoadingBrands ? (
                          <div className="text-sm text-muted-foreground py-3 text-center">
                            Carregando...
                          </div>
                        ) : brands.length === 0 ? (
                          <div className="text-sm text-muted-foreground py-3 text-center">
                            Nenhuma marca
                          </div>
                        ) : (
                          <ScrollArea className="h-40">
                            <div className="space-y-1">
                              {brands.map((brand) => (
                                <Button
                                  key={brand.id}
                                  className="w-full justify-start h-auto p-2 hover:bg-muted/50 bg-transparent text-left"
                                  onClick={() => handleSendToBrand(brand)}
                                >
                                  <div className="flex items-center text-sm gap-2 w-full">
                                    {brand.logo && (
                                      <img 
                                        src={brand.logo} 
                                        alt={brand.name}
                                        className="w-5 h-5 rounded object-cover"
                                      />
                                    )}
                                    <span className="text-sm font-medium truncate">
                                      {brand.name}
                                    </span>
                                  </div>
                                </Button>
                              ))}
                            </div>
                          </ScrollArea>
                        )}
                      </div>
                    </Card>
                  </PopoverContent>
                </Popover>
              )}
              
              <Protect role="org:admin">
                <Button 
                  size="sm"
                  className="h-8 px-3 gap-2 bg-destructive/10 hover:bg-destructive/20 text-destructive"
                  onClick={onDeleteSelected}
                >
                  <Trash2 className="h-3 w-3" />
                  <span className="text-xs hidden sm:inline">Excluir</span>
                </Button>
              </Protect>
              
              <Protect role="org:admin">
                <Button 
                  size="sm"
                  className="h-8 px-3 gap-2 bg-muted/50 hover:bg-muted text-muted-foreground hover:text-foreground"
                  onClick={onDuplicateSelected}
                >
                  <Copy className="h-3 w-3" />
                  <span className="text-xs hidden sm:inline">Duplicar</span>
                </Button>
              </Protect>
            </>
          ) : (
            // Controles principais
            <>
              {/* Controles de visualização */}
              <div className="flex items-center gap-1 bg-muted/30 rounded-lg p-1">
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      className={`h-6 w-6 p-0 ${
                        viewMode === 'grid' 
                          ? 'bg-gradient-to-r from-[#9810fa] to-[#ff0074] shadow-sm text-white' 
                          : 'text-gray-600 bg-white hover:bg-gray-100 border dark:text-gray-300 dark:bg-background dark:hover:bg-gray-700' 
                      }`}
                      onClick={() => onViewModeChange('grid')}
                    >
                      <GridIcon className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Grade</p>
                  </TooltipContent>
                </Tooltip>
                
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      size="sm"
                      className={`h-6 w-6 p-0 ${
                        viewMode === 'list' 
                          ? 'bg-gradient-to-r from-[#9810fa] to-[#ff0074] shadow-sm text-white' 
                          : 'text-gray-600 bg-white hover:bg-gray-100 border dark:text-gray-300 dark:bg-background dark:hover:bg-gray-700' 
                      }`}
                      onClick={() => onViewModeChange('list')}
                    >
                      <List className="h-3 w-3" />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Lista</p>
                  </TooltipContent>
                </Tooltip>
              </div>

         

              {/* Ações */}
              <Protect role="org:admin">
                <Link href="/notes" passHref>
                  <Button 
                    size="sm"
                    className="h-8 px-3 gap-2 bg-muted/50 hover:bg-muted text-muted-foreground hover:text-foreground"
                  >
                    <MessageSquare className="h-3 w-3" />
                    <span className="text-xs hidden lg:inline">Anotações</span>
                  </Button>
                </Link>
              </Protect>

              <Protect role="org:admin">
                <Button 
                  size="sm"
                  className={`h-8 px-3 gap-2 ${
                    selectionMode 
                      ? 'bg-muted text-foreground' 
                      : 'bg-muted/50 hover:bg-muted text-muted-foreground hover:text-foreground'
                  }`}
                  onClick={onToggleSelectionMode}
                >
                  <Settings className="h-3 w-3" />
                  <span className="text-xs hidden lg:inline">
                    {selectionMode ? 'Cancelar' : 'Selecionar'}
                  </span>
                </Button>
              </Protect>

              <Protect role="org:admin">
                <Button 
                  size="sm"
                  className="h-8 px-3 gap-2 bg-[#5600ce] hover:bg-[#4a00b8] text-white shadow-sm"
                  onClick={onAddInfluencer}
                >
                  <Plus className="h-3 w-3" />
                  <span className="text-xs hidden lg:inline font-medium">Adicionar</span>
                </Button>
              </Protect>
            </>
          )}
        </div>
      </div>
    </div>
  )
} 